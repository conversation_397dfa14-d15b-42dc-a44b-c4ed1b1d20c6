# WooCommerce Pickup Locations Plugin

A WordPress plugin that adds pickup location selection functionality to WooCommerce checkout pages, supporting both self-pickup points (自取點) and smart lockers (智能櫃).

## Features

- **Two Pickup Types:**
  - **自取點 (Self-pickup)**: Traditional pickup points with region → district → location hierarchy
  - **智能櫃 (Smart Lockers)**: Smart locker locations with region → district → locker hierarchy (same structure as self-pickup)

- **Dynamic Form Fields**: Form fields appear and disappear based on the selected pickup type
- **Cascading Dropdowns**: Location selection follows a logical hierarchy
- **Data-driven**: Uses JSON files for easy location data management
- **Native WooCommerce Styling**: Follows default WooCommerce checkout field appearance
- **Multilingual Ready**: Prepared for translation with text domain support

## File Structure

```
pickup-locations/
├── woocommerce-pickup-locations.php    # Main plugin file
├── assets/
│   ├── css/
│   │   └── pickup-locations.css        # Styling for the form fields
│   ├── js/
│   │   └── pickup-locations.js         # JavaScript for dynamic behavior
│   └── data/
│       ├── sf-selfpickup.json          # Self-pickup location data
│       └── locker-location.json        # Smart locker location data
└── README.md                           # This file
```

## Data Structure

### Self-pickup Locations (sf-selfpickup.json)
```json
{
  "香港島": {
    "上環": [
      "852M\t上環文樂商廈順豐站\t香港香港島中西區中上環文咸東街91號...",
      "852MD\t上環嘉安大廈順豐站\t香港香港島中西區上環新街市街15-27號..."
    ]
  }
}
```

### Smart Locker Locations (locker-location.json)
```json
{
  "新界": {
    "大圍": [
      "H852FG61P\t大圍名城一期順豐自助櫃\t香港新界沙田區大圍美田路1號...",
      "H852FG63P\t大圍名城三期順豐自助櫃\t香港新界沙田區大圍美田路1號..."
    ]
  }
}
```

## How It Works

1. **User selects shipping method**: Plugin automatically detects shipping methods
2. **Conditional display**: Pickup location fields are **shown** only when these shipping methods are selected:
   - "順豐到付 自取點"
   - "免運費 (順豐自取點)"
   - For all other shipping methods, pickup location fields are hidden
3. **User selects pickup type**: Choose between 自取點 or 智能櫃
4. **Dynamic field display**:
   - For 自取點: Shows Region → District → Location dropdowns
   - For 智能櫃: Shows Region → District → Locker dropdowns (same structure)
5. **Data validation**: Ensures all required fields are selected before checkout (only when pickup shipping is selected)
6. **Order storage**: Saves selection to order meta for admin and customer viewing

## Installation

1. Upload the plugin folder to `/wp-content/plugins/`
2. Activate the plugin through the WordPress admin
3. Ensure WooCommerce is installed and active
4. The pickup location fields will automatically appear on the checkout page

## Customization

- **Add new locations**: Edit the JSON files in `assets/data/`
- **Modify styling**: Edit `assets/css/pickup-locations.css`
- **Change behavior**: Modify `assets/js/pickup-locations.js`
- **Update labels**: Use WordPress translation functions or modify the plugin directly
- **Configure shipping methods**: Edit the `get_show_shipping_methods()` function in the main plugin file to add/remove shipping method names that should **show** the pickup location fields

## Requirements

- WordPress 5.0+
- WooCommerce 5.0+
- PHP 7.4+

## Support

This plugin is designed for Hong Kong pickup locations with Chinese language support. Modify the data files and labels as needed for other regions.
