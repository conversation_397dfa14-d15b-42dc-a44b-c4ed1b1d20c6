<?php
/**
 * Test script to verify shipping address replacement functionality
 * Add this to your WordPress site temporarily to test the functionality
 */

// Only run if WooCommerce is active
if (!class_exists('WooCommerce')) {
    die('WooCommerce is not active');
}

echo "<h2>Testing Pickup Location Shipping Address Replacement</h2>";

// Test 1: Check if our plugin class exists
if (class_exists('WC_Pickup_Locations')) {
    echo "<p>✅ Plugin class exists</p>";
} else {
    echo "<p>❌ Plugin class not found</p>";
}

// Test 2: Check if hooks are registered
$hooks = array(
    'woocommerce_checkout_update_order_meta' => 'replace_shipping_address_with_pickup',
    'woocommerce_checkout_update_order_meta' => 'debug_checkout_process'
);

foreach ($hooks as $hook => $function) {
    if (has_action($hook)) {
        echo "<p>✅ Hook '$hook' is registered</p>";
    } else {
        echo "<p>❌ Hook '$hook' is not registered</p>";
    }
}

// Test 3: Simulate pickup location data parsing
echo "<h3>Testing Pickup Location Data Parsing</h3>";

$test_pickup_data = "852M\t上環文樂商廈順豐站\t香港香港島中西區中上環文咸東街91號及永樂街117號文樂商業大廈地下A號舖 ^852M^";

$location_parts = explode("\t", $test_pickup_data);

if (count($location_parts) >= 3) {
    $pickup_id = $location_parts[0];
    $pickup_name = $location_parts[1];
    $pickup_address = $location_parts[2];
    
    // Clean up the address
    $pickup_address = preg_replace('/\s*\^[^^\s]*\^.*$/', '', $pickup_address);
    
    echo "<p>✅ Pickup data parsed successfully:</p>";
    echo "<ul>";
    echo "<li><strong>ID:</strong> " . esc_html($pickup_id) . "</li>";
    echo "<li><strong>Name:</strong> " . esc_html($pickup_name) . "</li>";
    echo "<li><strong>Address:</strong> " . esc_html($pickup_address) . "</li>";
    echo "</ul>";
    
    // Test city extraction
    if (preg_match('/(香港|九龍|新界)([^區]*區)?/', $pickup_address, $matches)) {
        $extracted_city = $matches[1] . (isset($matches[2]) ? $matches[2] : '');
        echo "<p>✅ City extracted: " . esc_html($extracted_city) . "</p>";
    } else {
        echo "<p>⚠️ Could not extract city from address</p>";
    }
} else {
    echo "<p>❌ Failed to parse pickup data</p>";
}

// Test 4: Check recent orders for pickup location data
echo "<h3>Recent Orders with Pickup Location Data</h3>";

$recent_orders = wc_get_orders(array(
    'limit' => 5,
    'status' => array('wc-processing', 'wc-completed', 'wc-pending'),
    'meta_query' => array(
        array(
            'key' => '_pickup_location',
            'compare' => 'EXISTS'
        )
    )
));

if (!empty($recent_orders)) {
    echo "<p>Found " . count($recent_orders) . " recent orders with pickup location data:</p>";
    foreach ($recent_orders as $order) {
        $pickup_location = $order->get_meta('_pickup_location');
        $shipping_address = $order->get_formatted_shipping_address();
        
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
        echo "<strong>Order #" . $order->get_id() . "</strong><br>";
        echo "<strong>Pickup Location:</strong> " . esc_html($pickup_location) . "<br>";
        echo "<strong>Shipping Address:</strong><br>" . $shipping_address;
        echo "</div>";
    }
} else {
    echo "<p>No recent orders found with pickup location data</p>";
}

echo "<hr>";
echo "<p><strong>Instructions:</strong></p>";
echo "<ol>";
echo "<li>Place a test order with pickup location selected</li>";
echo "<li>Check your WordPress error logs for debug messages</li>";
echo "<li>Refresh this page to see if the order appears above</li>";
echo "<li>Check the order in WooCommerce admin to see if shipping address was replaced</li>";
echo "</ol>";
?>
