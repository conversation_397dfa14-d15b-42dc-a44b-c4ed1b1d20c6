<?php
/**
 * Plugin Name: WooCommerce Pickup Locations
 * Description: Adds cascading dropdown menus for pickup location selection on WooCommerce checkout page.
 * Version: 1.2.0
 * Author: MMM
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: wc-pickup-locations
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.3
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if WooCommerce is active
if (!in_array('woocommerce/woocommerce.php', apply_filters('active_plugins', get_option('active_plugins')))) {
    return;
}

class WC_Pickup_Locations {

    public function __construct() {
        add_action('init', array($this, 'init'));
    }

    public function init() {
        // Define plugin constants
        define('WC_PICKUP_LOCATIONS_VERSION', '1.0.0');
        define('WC_PICKUP_LOCATIONS_PLUGIN_DIR', plugin_dir_path(__FILE__));
        define('WC_PICKUP_LOCATIONS_PLUGIN_URL', plugin_dir_url(__FILE__));

        // Load plugin text domain
        add_action('plugins_loaded', array($this, 'load_textdomain'));

        // Add checkout fields
        add_action('woocommerce_after_checkout_billing_form', array($this, 'add_pickup_location_fields'));

        // Validate fields
        add_action('woocommerce_checkout_process', array($this, 'validate_pickup_location_fields'));

        // Save fields
        add_action('woocommerce_checkout_update_order_meta', array($this, 'save_pickup_location_fields'));

        // Display in admin
        add_action('woocommerce_admin_order_data_after_billing_address', array($this, 'display_pickup_location_admin'));

        // Display in customer order details
        add_action('woocommerce_order_details_after_customer_details', array($this, 'display_pickup_location_customer'));

        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Replace shipping address with pickup location
        add_action('woocommerce_checkout_update_order_meta', array($this, 'replace_shipping_address_with_pickup'), 20);

        // Also replace shipping address during checkout process
        add_filter('woocommerce_checkout_get_value', array($this, 'override_shipping_fields'), 10, 2);
    }

    public function load_textdomain() {
        load_plugin_textdomain('wc-pickup-locations', false, dirname(plugin_basename(__FILE__)) . '/languages/');
    }

    public function add_pickup_location_fields($checkout) {
        echo '<div id="pickup_location_fields" style="display: none;">';

        // Pickup Type dropdown - Updated with two options
        woocommerce_form_field('pickup_type', array(
            'type' => 'select',
            'class' => array('form-row-wide'),
            'label' => __('選擇自取方式', 'wc-pickup-locations'),
            'required' => true,
            'options' => array(
                '' => __('選擇自取方式...', 'wc-pickup-locations'),
                'selfpickup' => __('自取點', 'wc-pickup-locations'),
                'locker' => __('智能櫃', 'wc-pickup-locations')
            )
        ), $checkout->get_value('pickup_type'));

        // Region dropdown - Updated to match JSON keys
        woocommerce_form_field('pickup_region', array(
            'type' => 'select',
            'class' => array('form-row-wide'),
            'label' => __('選擇區域', 'wc-pickup-locations'),
            'required' => true,
            'options' => array(
                '' => __('選擇區域...', 'wc-pickup-locations'),
                '香港島' => __('香港島', 'wc-pickup-locations'),
                '九龍' => __('九龍', 'wc-pickup-locations'),
                '新界' => __('新界', 'wc-pickup-locations')
            )
        ), $checkout->get_value('pickup_region'));

        // District dropdown (initially hidden, label changes based on pickup type)
        woocommerce_form_field('pickup_district', array(
            'type' => 'select',
            'class' => array('form-row-wide'),
            'label' => __('選擇地區', 'wc-pickup-locations'),
            'required' => true,
            'options' => array('' => __('選擇地區...', 'wc-pickup-locations'))
        ), $checkout->get_value('pickup_district'));

        // Specific location dropdown (initially hidden)
        woocommerce_form_field('pickup_location', array(
            'type' => 'select',
            'class' => array('form-row-wide'),
            'label' => __('選擇位置', 'wc-pickup-locations'),
            'required' => true,
            'options' => array('' => __('選擇位置...', 'wc-pickup-locations'))
        ), $checkout->get_value('pickup_location'));

        echo '</div>';
    }

    public function validate_pickup_location_fields() {
        // Check if shipping method should show pickup fields
        if (!$this->should_show_pickup_fields()) {
            return; // Skip validation if pickup fields should not be shown
        }

        if (empty($_POST['pickup_type'])) {
            wc_add_notice(__('Please select a pickup type.', 'wc-pickup-locations'), 'error');
            return;
        }

        $pickup_type = sanitize_text_field($_POST['pickup_type']);

        // Both pickup types now use the same structure, so all fields are required
        if (empty($_POST['pickup_region'])) {
            wc_add_notice(__('Please select a region.', 'wc-pickup-locations'), 'error');
        }
        if (empty($_POST['pickup_district'])) {
            wc_add_notice(__('Please select a district.', 'wc-pickup-locations'), 'error');
        }
        if (empty($_POST['pickup_location'])) {
            if ($pickup_type === 'locker') {
                wc_add_notice(__('Please select a locker.', 'wc-pickup-locations'), 'error');
            } else {
                wc_add_notice(__('Please select a pickup location.', 'wc-pickup-locations'), 'error');
            }
        }
    }

    /**
     * Check if pickup location fields should be shown based on selected shipping method
     */
    private function should_show_pickup_fields() {
        if (empty($_POST['shipping_method']) || !is_array($_POST['shipping_method'])) {
            error_log('WC Pickup Locations: No shipping method found in POST data');
            return false; // Hide fields if no shipping method is selected
        }

        $show_for_shipping_methods = $this->get_show_shipping_methods();

        error_log('WC Pickup Locations: Checking shipping methods: ' . print_r($_POST['shipping_method'], true));
        error_log('WC Pickup Locations: Looking for methods: ' . print_r($show_for_shipping_methods, true));

        foreach ($_POST['shipping_method'] as $shipping_method) {
            error_log('WC Pickup Locations: Checking shipping method: ' . $shipping_method);

            // Check if the shipping method contains any of the methods that should show pickup fields
            foreach ($show_for_shipping_methods as $show_method) {
                if (strpos($shipping_method, $show_method) !== false) {
                    error_log('WC Pickup Locations: Match found! Showing pickup fields for: ' . $show_method);
                    return true; // Show pickup fields
                }
            }
        }

        error_log('WC Pickup Locations: No matching shipping method found, hiding pickup fields');
        return false; // Hide pickup fields for all other shipping methods
    }

    /**
     * Get the list of shipping methods that should show pickup location fields
     */
    private function get_show_shipping_methods() {
        return array(
            '順豐到付 自取點',
            '免運費 (順豐自取點)',
            // Add partial matches as fallback
            '自取點',
            'pickup',
            'self_pickup'
        );
    }

    /**
     * Check if shipping address replacement is enabled
     */
    private function is_shipping_address_replacement_enabled() {
        // You can add a setting here later, for now it's always enabled
        return true;
    }

    public function save_pickup_location_fields($order_id) {
        // Only save pickup location data if pickup fields should be shown
        if (!$this->should_show_pickup_fields()) {
            return;
        }

        if (!empty($_POST['pickup_type'])) {
            update_post_meta($order_id, '_pickup_type', sanitize_text_field($_POST['pickup_type']));
        }
        if (!empty($_POST['pickup_region'])) {
            update_post_meta($order_id, '_pickup_region', sanitize_text_field($_POST['pickup_region']));
        }
        if (!empty($_POST['pickup_district'])) {
            update_post_meta($order_id, '_pickup_district', sanitize_text_field($_POST['pickup_district']));
        }
        if (!empty($_POST['pickup_location'])) {
            update_post_meta($order_id, '_pickup_location', sanitize_text_field($_POST['pickup_location']));
        }
    }

    /**
     * Replace shipping address with pickup location details
     */
    public function replace_shipping_address_with_pickup($order_id) {
        // Only replace address if pickup fields should be shown
        if (!$this->should_show_pickup_fields()) {
            return;
        }

        // Check if shipping address replacement is enabled
        if (!$this->is_shipping_address_replacement_enabled()) {
            return;
        }

        // Get the pickup location data
        $pickup_location = get_post_meta($order_id, '_pickup_location', true);

        if (empty($pickup_location)) {
            return;
        }

        // Parse the pickup location data (format: ID\tName\tAddress)
        $location_parts = explode("\t", $pickup_location);

        if (count($location_parts) >= 3) {
            $pickup_id = $location_parts[0];
            $pickup_name = $location_parts[1];
            $pickup_address = $location_parts[2];

            // Clean up the address (remove trailing codes like ^ID^)
            $pickup_address = preg_replace('/\s*\^[^^\s]*\^.*$/', '', $pickup_address);

            // Get the order object
            $order = wc_get_order($order_id);

            if ($order) {
                // Replace shipping address with pickup location
                $order->set_shipping_first_name($pickup_name); // Use pickup name as first name
                $order->set_shipping_last_name(''); // Clear last name
                $order->set_shipping_company($pickup_name); // Also set as company
                $order->set_shipping_address_1($pickup_address);
                $order->set_shipping_address_2(''); // Clear address line 2
                $order->set_shipping_city(''); // We'll extract city from address if needed
                $order->set_shipping_state(''); // Clear state
                $order->set_shipping_postcode(''); // Clear postcode
                $order->set_shipping_country('HK'); // Set to Hong Kong

                // Extract city from address if possible (look for common HK area names)
                if (preg_match('/(香港|九龍|新界)([^區]*區)?([^街道]*)(街|道|路)/', $pickup_address, $matches)) {
                    $extracted_city = $matches[1] . (isset($matches[2]) ? $matches[2] : '');
                    $order->set_shipping_city($extracted_city);
                }

                // Save the order
                $order->save();

                // Add order note
                $order->add_order_note(
                    sprintf(
                        __('Shipping address replaced with pickup location: %s - %s', 'wc-pickup-locations'),
                        $pickup_name,
                        $pickup_address
                    )
                );
            }
        }
    }

    public function display_pickup_location_admin($order) {
        $pickup_type = get_post_meta($order->get_id(), '_pickup_type', true);
        $pickup_region = get_post_meta($order->get_id(), '_pickup_region', true);
        $pickup_district = get_post_meta($order->get_id(), '_pickup_district', true);
        $pickup_location = get_post_meta($order->get_id(), '_pickup_location', true);

        if ($pickup_type || $pickup_region || $pickup_district || $pickup_location) {
            echo '<h3>' . __('Pickup Location Details', 'wc-pickup-locations') . '</h3>';

            if ($pickup_type) {
                $type_labels = array(
                    'selfpickup' => '自取點',
                    'locker' => '智能櫃'
                );
                $type_label = isset($type_labels[$pickup_type]) ? $type_labels[$pickup_type] : $pickup_type;
                echo '<p><strong>' . __('Pickup Type:', 'wc-pickup-locations') . '</strong> ' . esc_html($type_label) . '</p>';
            }

            if ($pickup_region) {
                echo '<p><strong>' . __('Region:', 'wc-pickup-locations') . '</strong> ' . esc_html($pickup_region) . '</p>';
            }

            if ($pickup_district) {
                echo '<p><strong>' . __('District:', 'wc-pickup-locations') . '</strong> ' . esc_html($pickup_district) . '</p>';
            }

            if ($pickup_location) {
                // Parse and display the pickup location nicely
                $location_parts = explode("\t", $pickup_location);
                if (count($location_parts) >= 3) {
                    $pickup_id = $location_parts[0];
                    $pickup_name = $location_parts[1];
                    $pickup_address = preg_replace('/\s*\^[^^\s]*\^.*$/', '', $location_parts[2]);

                    echo '<p><strong>' . __('Pickup ID:', 'wc-pickup-locations') . '</strong> ' . esc_html($pickup_id) . '</p>';
                    echo '<p><strong>' . __('Pickup Name:', 'wc-pickup-locations') . '</strong> ' . esc_html($pickup_name) . '</p>';
                    echo '<p><strong>' . __('Pickup Address:', 'wc-pickup-locations') . '</strong> ' . esc_html($pickup_address) . '</p>';
                } else {
                    echo '<p><strong>' . __('Location:', 'wc-pickup-locations') . '</strong> ' . esc_html($pickup_location) . '</p>';
                }
            }

            // Show note about shipping address replacement
            echo '<p><em>' . __('Note: The shipping address has been replaced with the pickup location details.', 'wc-pickup-locations') . '</em></p>';
        }
    }

    public function display_pickup_location_customer($order) {
        $pickup_type = get_post_meta($order->get_id(), '_pickup_type', true);
        $pickup_region = get_post_meta($order->get_id(), '_pickup_region', true);
        $pickup_district = get_post_meta($order->get_id(), '_pickup_district', true);
        $pickup_location = get_post_meta($order->get_id(), '_pickup_location', true);

        if ($pickup_type || $pickup_region || $pickup_district || $pickup_location) {
            echo '<h2>' . __('Pickup Location', 'wc-pickup-locations') . '</h2>';
            echo '<table class="woocommerce-table shop_table pickup-location-table">';
            if ($pickup_type) {
                $type_labels = array(
                    'selfpickup' => '自取點',
                    'locker' => '智能櫃'
                );
                $type_label = isset($type_labels[$pickup_type]) ? $type_labels[$pickup_type] : $pickup_type;
                echo '<tr><th>' . __('Pickup Type:', 'wc-pickup-locations') . '</th><td>' . esc_html($type_label) . '</td></tr>';
            }
            if ($pickup_region) {
                echo '<tr><th>' . __('Region:', 'wc-pickup-locations') . '</th><td>' . esc_html($pickup_region) . '</td></tr>';
            }
            if ($pickup_district) {
                echo '<tr><th>' . __('District:', 'wc-pickup-locations') . '</th><td>' . esc_html($pickup_district) . '</td></tr>';
            }
            if ($pickup_location) {
                echo '<tr><th>' . __('Location:', 'wc-pickup-locations') . '</th><td>' . esc_html($pickup_location) . '</td></tr>';
            }
            echo '</table>';
        }
    }

    private function get_readable_name($slug) {
        return ucwords(str_replace('_', ' ', $slug));
    }

    public function enqueue_scripts() {
        if (is_checkout()) {
            wp_enqueue_script(
                'wc-pickup-locations-js',
                WC_PICKUP_LOCATIONS_PLUGIN_URL . 'assets/js/pickup-locations.js',
                array('jquery'),
                WC_PICKUP_LOCATIONS_VERSION,
                true
            );

            wp_enqueue_style(
                'wc-pickup-locations-css',
                WC_PICKUP_LOCATIONS_PLUGIN_URL . 'assets/css/pickup-locations.css',
                array(),
                WC_PICKUP_LOCATIONS_VERSION
            );

            // Localize script with location data and configuration
            wp_localize_script('wc-pickup-locations-js', 'pickupLocations', $this->get_location_data());
            wp_localize_script('wc-pickup-locations-js', 'pickupLocationConfig', array(
                'showForShippingMethods' => $this->get_show_shipping_methods(),
                'debug' => defined('WP_DEBUG') && WP_DEBUG
            ));
        }
    }

    private function get_location_data() {
        $location_data = array();

        // Load self-pickup locations
        $selfpickup_file = WC_PICKUP_LOCATIONS_PLUGIN_DIR . 'assets/data/sf-selfpickup.json';
        $selfpickup_data = $this->load_json_file($selfpickup_file, 'self-pickup');
        if (!empty($selfpickup_data)) {
            $location_data['selfpickup'] = $selfpickup_data;
        }

        // Load locker locations
        $locker_file = WC_PICKUP_LOCATIONS_PLUGIN_DIR . 'assets/data/locker-location.json';
        $locker_data = $this->load_json_file($locker_file, 'locker');
        if (!empty($locker_data)) {
            $location_data['locker'] = $locker_data;
        }

        // Debug: Log successful data loading
        error_log('WC Pickup Locations: Successfully loaded pickup location data for ' . count($location_data) . ' pickup types');
        error_log('WC Pickup Locations: Available types: ' . implode(', ', array_keys($location_data)));

        // Debug: Log structure for each type
        foreach ($location_data as $type => $data) {
            if (is_array($data)) {
                error_log('WC Pickup Locations: ' . $type . ' has regions: ' . implode(', ', array_keys($data)));
            }
        }

        return $location_data;
    }

    private function load_json_file($file_path, $type) {
        // Debug: Log the full path for troubleshooting
        error_log('WC Pickup Locations: Looking for ' . $type . ' JSON file at: ' . $file_path);

        // Check if the JSON file exists
        if (!file_exists($file_path)) {
            // Log error and return empty array as fallback
            error_log('WC Pickup Locations: ' . $type . ' JSON file not found at ' . $file_path);
            return array();
        }

        // Read the JSON file
        $json_content = file_get_contents($file_path);

        if ($json_content === false) {
            // Log error and return empty array as fallback
            error_log('WC Pickup Locations: Failed to read ' . $type . ' JSON file at ' . $file_path);
            return array();
        }

        // Decode the JSON content
        $data = json_decode($json_content, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            // Log JSON decode error and return empty array as fallback
            error_log('WC Pickup Locations: JSON decode error for ' . $type . ' - ' . json_last_error_msg());
            return array();
        }

        // Debug: Log successful data loading
        error_log('WC Pickup Locations: Successfully loaded ' . count($data) . ' regions from ' . $type . ' JSON file');

        return $data;
    }
}

// Initialize the plugin
new WC_Pickup_Locations();

// Activation hook
register_activation_hook(__FILE__, 'wc_pickup_locations_activate');
function wc_pickup_locations_activate() {
    // Check if WooCommerce is active
    if (!class_exists('WooCommerce')) {
        deactivate_plugins(plugin_basename(__FILE__));
        wp_die(__('This plugin requires WooCommerce to be installed and active.', 'wc-pickup-locations'));
    }
}

// Deactivation hook
register_deactivation_hook(__FILE__, 'wc_pickup_locations_deactivate');
function wc_pickup_locations_deactivate() {
    // Clean up if needed
}
?>