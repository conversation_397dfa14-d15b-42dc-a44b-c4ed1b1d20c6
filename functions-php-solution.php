<?php
/**
 * SIMPLE SOLUTION FOR FUNCTIONS.PHP
 * Copy this code to your theme's functions.php file
 * This bypasses all plugin complexity and directly modifies checkout data
 */

// Method 1: Filter checkout data before order creation
add_filter('woocommerce_checkout_posted_data', 'force_replace_shipping_with_pickup', 999);

function force_replace_shipping_with_pickup($data) {
    // Log for debugging
    error_log('FUNCTIONS.PHP: Checkout data filter called');
    
    // Check if pickup location exists
    $pickup_location = '';
    if (isset($data['pickup_location']) && !empty($data['pickup_location'])) {
        $pickup_location = $data['pickup_location'];
    } elseif (isset($_POST['pickup_location']) && !empty($_POST['pickup_location'])) {
        $pickup_location = $_POST['pickup_location'];
    }
    
    if (empty($pickup_location)) {
        error_log('FUNCTIONS.PHP: No pickup location found');
        return $data;
    }
    
    // Check if it's a pickup shipping method
    $is_pickup_method = false;
    if (isset($data['shipping_method']) && is_array($data['shipping_method'])) {
        foreach ($data['shipping_method'] as $method) {
            if (strpos($method, '自取點') !== false) {
                $is_pickup_method = true;
                break;
            }
        }
    }
    
    if (!$is_pickup_method) {
        error_log('FUNCTIONS.PHP: Not a pickup shipping method');
        return $data;
    }
    
    // Parse pickup location
    $parts = explode("\t", $pickup_location);
    if (count($parts) >= 3) {
        $pickup_name = trim($parts[1]);
        $pickup_address = preg_replace('/\s*\^[^^\s]*\^.*$/', '', trim($parts[2]));
        
        // Replace shipping data
        $data['shipping_first_name'] = $pickup_name;
        $data['shipping_last_name'] = '';
        $data['shipping_company'] = $pickup_name;
        $data['shipping_address_1'] = $pickup_address;
        $data['shipping_address_2'] = '';
        $data['shipping_city'] = '';
        $data['shipping_state'] = '';
        $data['shipping_postcode'] = '';
        $data['shipping_country'] = 'HK';
        
        error_log('FUNCTIONS.PHP: Shipping data replaced with: ' . $pickup_name . ' - ' . $pickup_address);
    }
    
    return $data;
}

// Method 2: Direct order modification after creation (backup)
add_action('woocommerce_checkout_update_order_meta', 'force_update_order_shipping_address', 999);

function force_update_order_shipping_address($order_id) {
    error_log('FUNCTIONS.PHP: Order update hook called for order ' . $order_id);
    
    // Get pickup location
    $pickup_location = '';
    if (isset($_POST['pickup_location']) && !empty($_POST['pickup_location'])) {
        $pickup_location = sanitize_text_field($_POST['pickup_location']);
    }
    
    if (empty($pickup_location)) {
        error_log('FUNCTIONS.PHP: No pickup location in POST data');
        return;
    }
    
    // Parse and apply
    $parts = explode("\t", $pickup_location);
    if (count($parts) >= 3) {
        $pickup_name = trim($parts[1]);
        $pickup_address = preg_replace('/\s*\^[^^\s]*\^.*$/', '', trim($parts[2]));
        
        $order = wc_get_order($order_id);
        if ($order) {
            $order->set_shipping_first_name($pickup_name);
            $order->set_shipping_last_name('');
            $order->set_shipping_company($pickup_name);
            $order->set_shipping_address_1($pickup_address);
            $order->set_shipping_address_2('');
            $order->set_shipping_city('');
            $order->set_shipping_state('');
            $order->set_shipping_postcode('');
            $order->set_shipping_country('HK');
            $order->save();
            
            $order->add_order_note('Shipping address replaced with pickup location: ' . $pickup_name);
            error_log('FUNCTIONS.PHP: Order shipping address updated successfully');
        }
    }
}

// Method 3: Brute force - check all new orders every minute
add_action('wp_loaded', function() {
    if (!wp_next_scheduled('check_and_fix_pickup_orders')) {
        wp_schedule_event(time(), 'hourly', 'check_and_fix_pickup_orders');
    }
});

add_action('check_and_fix_pickup_orders', 'fix_pickup_orders_shipping_address');

function fix_pickup_orders_shipping_address() {
    // Get recent orders with pickup location meta but original shipping address
    $orders = wc_get_orders(array(
        'limit' => 50,
        'status' => array('processing', 'pending', 'on-hold'),
        'date_created' => '>' . (time() - 3600), // Last hour
        'meta_query' => array(
            array(
                'key' => '_pickup_location',
                'compare' => 'EXISTS'
            )
        )
    ));
    
    foreach ($orders as $order) {
        $pickup_location = $order->get_meta('_pickup_location');
        $current_shipping_name = $order->get_shipping_first_name();
        
        // If pickup location exists but shipping name doesn't match pickup name
        if ($pickup_location && !empty($pickup_location)) {
            $parts = explode("\t", $pickup_location);
            if (count($parts) >= 3) {
                $pickup_name = trim($parts[1]);
                
                // If shipping name doesn't match pickup name, fix it
                if ($current_shipping_name !== $pickup_name) {
                    $pickup_address = preg_replace('/\s*\^[^^\s]*\^.*$/', '', trim($parts[2]));
                    
                    $order->set_shipping_first_name($pickup_name);
                    $order->set_shipping_company($pickup_name);
                    $order->set_shipping_address_1($pickup_address);
                    $order->set_shipping_country('HK');
                    $order->save();
                    
                    $order->add_order_note('Shipping address automatically corrected to pickup location');
                    error_log('FUNCTIONS.PHP: Fixed shipping address for order ' . $order->get_id());
                }
            }
        }
    }
}

// Test function - add ?test_functions_php=1 to any page to test
add_action('init', function() {
    if (isset($_GET['test_functions_php']) && current_user_can('manage_options')) {
        echo "<h2>Functions.php Solution Test</h2>";
        
        // Test data parsing
        $test_pickup = "852M\t上環文樂商廈順豐站\t香港香港島中西區中上環文咸東街91號及永樂街117號文樂商業大廈地下A號舖 ^852M^";
        $parts = explode("\t", $test_pickup);
        
        if (count($parts) >= 3) {
            $pickup_name = trim($parts[1]);
            $pickup_address = preg_replace('/\s*\^[^^\s]*\^.*$/', '', trim($parts[2]));
            
            echo "<p>✅ Data parsing works:</p>";
            echo "<p><strong>Name:</strong> " . esc_html($pickup_name) . "</p>";
            echo "<p><strong>Address:</strong> " . esc_html($pickup_address) . "</p>";
        }
        
        echo "<p>Functions.php solution is active. Place a test order to verify.</p>";
        exit;
    }
});
?>
