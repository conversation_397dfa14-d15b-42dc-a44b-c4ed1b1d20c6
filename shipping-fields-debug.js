// Debug script to find shipping field selectors in Bricks Builder
// Paste this in your browser console on the checkout page

console.log('=== SHIPPING FIELDS DEBUG ===');

// List of field names to check
var fieldNames = ['first_name', 'last_name', 'company', 'address_1', 'address_2', 'city', 'state', 'postcode', 'country'];

// Possible selectors for each field
var selectorPatterns = [
    '#shipping_{field}',
    'input[name="shipping_{field}"]',
    'select[name="shipping_{field}"]',
    '[data-field="shipping_{field}"] input',
    '[data-field="shipping_{field}"] select',
    '.shipping-{field} input',
    '.shipping-{field} select',
    '[id*="shipping_{field}"]',
    '[name*="shipping_{field}"]',
    '.woocommerce-shipping-fields input[name*="{field}"]',
    '.woocommerce-shipping-fields select[name*="{field}"]'
];

fieldNames.forEach(function(fieldName) {
    console.log('\n--- Checking field: ' + fieldName + ' ---');
    
    var foundSelectors = [];
    
    selectorPatterns.forEach(function(pattern) {
        var selector = pattern.replace(/{field}/g, fieldName);
        var elements = document.querySelectorAll(selector);
        
        if (elements.length > 0) {
            foundSelectors.push({
                selector: selector,
                count: elements.length,
                elements: elements
            });
            
            console.log('✅ Found with selector: ' + selector + ' (' + elements.length + ' elements)');
            
            // Log element details
            elements.forEach(function(el, index) {
                console.log('  Element ' + index + ':', {
                    tagName: el.tagName,
                    id: el.id,
                    name: el.name,
                    className: el.className,
                    value: el.value,
                    type: el.type
                });
            });
        }
    });
    
    if (foundSelectors.length === 0) {
        console.log('❌ No elements found for field: ' + fieldName);
    }
});

// Also check for any input/select that might be shipping-related
console.log('\n=== ALL SHIPPING-RELATED INPUTS ===');
var allInputs = document.querySelectorAll('input, select');
var shippingInputs = [];

allInputs.forEach(function(input) {
    if (input.name && (input.name.includes('shipping') || input.id.includes('shipping'))) {
        shippingInputs.push(input);
    }
});

console.log('Found ' + shippingInputs.length + ' shipping-related inputs:');
shippingInputs.forEach(function(input, index) {
    console.log(index + ':', {
        tagName: input.tagName,
        id: input.id,
        name: input.name,
        className: input.className,
        type: input.type,
        value: input.value
    });
});

// Test function to update fields
window.testShippingFieldUpdate = function() {
    console.log('\n=== TESTING FIELD UPDATES ===');
    
    var testData = {
        name: '上環文樂商廈順豐站',
        address: '香港香港島中西區中上環文咸東街91號及永樂街117號文樂商業大廈地下A號舖'
    };
    
    // Try to update first_name field
    var firstNameSelectors = [
        '#shipping_first_name',
        'input[name="shipping_first_name"]',
        '[data-field="shipping_first_name"] input'
    ];
    
    var updated = false;
    firstNameSelectors.forEach(function(selector) {
        var field = document.querySelector(selector);
        if (field) {
            console.log('Updating field with selector: ' + selector);
            field.value = testData.name;
            field.dispatchEvent(new Event('change', { bubbles: true }));
            updated = true;
        }
    });
    
    if (updated) {
        console.log('✅ Successfully updated shipping first name field');
    } else {
        console.log('❌ Could not find shipping first name field to update');
    }
};

console.log('\n=== DEBUG COMPLETE ===');
console.log('Run testShippingFieldUpdate() to test field updates');

// Auto-run the test function
setTimeout(function() {
    if (typeof testShippingFieldUpdate === 'function') {
        testShippingFieldUpdate();
    }
}, 1000);
