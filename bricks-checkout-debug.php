<?php
/**
 * Debug script specifically for Bricks Builder checkout issues
 * Add this as a plugin or to functions.php temporarily
 */

// <PERSON> into all possible checkout-related actions to see what's happening
add_action('init', function() {
    if (!is_admin() && isset($_GET['debug_bricks_checkout'])) {
        
        // Add debug hooks for all checkout-related actions
        $checkout_hooks = array(
            'woocommerce_checkout_process',
            'woocommerce_checkout_update_order_meta',
            'woocommerce_checkout_create_order',
            'woocommerce_new_order',
            'woocommerce_thankyou',
            'woocommerce_payment_complete',
            'woocommerce_order_status_changed'
        );
        
        foreach ($checkout_hooks as $hook) {
            add_action($hook, function($order_id) use ($hook) {
                error_log("BRICKS DEBUG: Hook '$hook' fired for order: " . $order_id);
                error_log("BRICKS DEBUG: POST data available: " . (empty($_POST) ? 'NO' : 'YES'));
                if (isset($_POST['pickup_location'])) {
                    error_log("BRICKS DEBUG: Pickup location in POST: " . $_POST['pickup_location']);
                }
            }, 1);
        }
        
        // Debug Bricks-specific hooks if they exist
        if (class_exists('Bricks\Frontend')) {
            add_action('bricks/frontend/render_element', function($element) {
                if (isset($element['name']) && strpos($element['name'], 'form') !== false) {
                    error_log("BRICKS DEBUG: Rendering form element: " . print_r($element, true));
                }
            });
        }
    }
});

// Add a simple form submission logger
add_action('wp_loaded', function() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && !is_admin()) {
        error_log("BRICKS DEBUG: POST request detected on: " . $_SERVER['REQUEST_URI']);
        error_log("BRICKS DEBUG: POST data keys: " . implode(', ', array_keys($_POST)));
        
        // Check for WooCommerce checkout
        if (isset($_POST['woocommerce_checkout_place_order']) || 
            isset($_POST['payment_method']) || 
            strpos($_SERVER['REQUEST_URI'], 'checkout') !== false) {
            error_log("BRICKS DEBUG: This looks like a checkout submission");
            
            if (isset($_POST['pickup_location'])) {
                error_log("BRICKS DEBUG: Pickup location found: " . $_POST['pickup_location']);
            } else {
                error_log("BRICKS DEBUG: No pickup location in POST data");
            }
        }
    }
});

// Create a test order manually to verify our replacement works
add_action('wp_ajax_test_manual_order_replacement', function() {
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized');
    }
    
    // Create a test order
    $order = wc_create_order();
    $order->set_billing_first_name('Test');
    $order->set_billing_last_name('Customer');
    $order->set_billing_email('<EMAIL>');
    
    // Set original shipping address
    $order->set_shipping_first_name('Original');
    $order->set_shipping_last_name('Address');
    $order->set_shipping_address_1('123 Original Street');
    $order->set_shipping_city('Original City');
    $order->set_shipping_country('HK');
    
    $order->save();
    
    echo "<h2>Test Order Created: #" . $order->get_id() . "</h2>";
    echo "<p><strong>Original Shipping Address:</strong><br>" . $order->get_formatted_shipping_address() . "</p>";
    
    // Test our replacement function
    $test_pickup_data = "852M\t上環文樂商廈順豐站\t香港香港島中西區中上環文咸東街91號及永樂街117號文樂商業大廈地下A號舖 ^852M^";
    
    // Simulate the replacement
    $parts = explode("\t", $test_pickup_data);
    if (count($parts) >= 3) {
        $pickup_name = $parts[1];
        $pickup_address = preg_replace('/\s*\^[^^\s]*\^.*$/', '', $parts[2]);
        
        $order->set_shipping_first_name($pickup_name);
        $order->set_shipping_company($pickup_name);
        $order->set_shipping_address_1($pickup_address);
        $order->set_shipping_country('HK');
        $order->save();
        
        echo "<p><strong>After Replacement:</strong><br>" . $order->get_formatted_shipping_address() . "</p>";
        echo "<p>✅ Manual replacement test successful!</p>";
    }
    
    exit;
});

// Add admin notice with instructions
add_action('admin_notices', function() {
    if (current_user_can('manage_options')) {
        echo '<div class="notice notice-info">';
        echo '<p><strong>Bricks Checkout Debug:</strong></p>';
        echo '<p>1. Add <code>?debug_bricks_checkout=1</code> to your checkout URL to enable debugging</p>';
        echo '<p>2. <a href="' . admin_url('admin-ajax.php?action=test_manual_order_replacement') . '" target="_blank">Test Manual Order Replacement</a></p>';
        echo '<p>3. Check error logs for debug information</p>';
        echo '</div>';
    }
});
?>
