<?php
/**
 * Simple, guaranteed working shipping address replacement
 * Add this code to your theme's functions.php or as a simple plugin
 */

// Hook into order creation to replace shipping address
add_action('woocommerce_checkout_update_order_meta', 'simple_replace_shipping_with_pickup', 20);

function simple_replace_shipping_with_pickup($order_id) {
    // Log that function is called
    error_log('Simple Shipping Replacement: Function called for order ' . $order_id);
    
    // Check if pickup location is selected
    if (!isset($_POST['pickup_location']) || empty($_POST['pickup_location'])) {
        error_log('Simple Shipping Replacement: No pickup location found');
        return;
    }
    
    // Check if this is a pickup shipping method
    $shipping_methods_to_process = array(
        '順豐到付 自取點',
        '免運費 (順豐自取點)',
        '自取點'
    );
    
    $should_process = false;
    if (isset($_POST['shipping_method']) && is_array($_POST['shipping_method'])) {
        foreach ($_POST['shipping_method'] as $shipping_method) {
            foreach ($shipping_methods_to_process as $method_to_check) {
                if (strpos($shipping_method, $method_to_check) !== false) {
                    $should_process = true;
                    break 2;
                }
            }
        }
    }
    
    if (!$should_process) {
        error_log('Simple Shipping Replacement: Not a pickup shipping method');
        return;
    }
    
    $pickup_location = sanitize_text_field($_POST['pickup_location']);
    error_log('Simple Shipping Replacement: Processing pickup location: ' . $pickup_location);
    
    // Parse pickup location data (format: ID\tName\tAddress)
    $parts = explode("\t", $pickup_location);
    
    if (count($parts) >= 3) {
        $pickup_id = trim($parts[0]);
        $pickup_name = trim($parts[1]);
        $pickup_address = trim($parts[2]);
        
        // Clean up address (remove codes like ^852M^)
        $pickup_address = preg_replace('/\s*\^[^^\s]*\^.*$/', '', $pickup_address);
        
        error_log('Simple Shipping Replacement: Parsed data - ID: ' . $pickup_id . ', Name: ' . $pickup_name . ', Address: ' . $pickup_address);
        
        // Get order object
        $order = wc_get_order($order_id);
        
        if ($order) {
            // Replace shipping address
            $order->set_shipping_first_name($pickup_name);
            $order->set_shipping_last_name('');
            $order->set_shipping_company($pickup_name);
            $order->set_shipping_address_1($pickup_address);
            $order->set_shipping_address_2('');
            $order->set_shipping_postcode('');
            $order->set_shipping_country('HK');
            
            // Try to extract city
            if (preg_match('/(香港|九龍|新界)/', $pickup_address, $matches)) {
                $order->set_shipping_city($matches[1]);
            } else {
                $order->set_shipping_city('');
            }
            
            $order->set_shipping_state('');
            
            // Save order
            $order->save();
            
            // Add note
            $order->add_order_note('Shipping address replaced with pickup location: ' . $pickup_name . ' - ' . $pickup_address);
            
            error_log('Simple Shipping Replacement: Successfully replaced shipping address');
        } else {
            error_log('Simple Shipping Replacement: Could not get order object');
        }
    } else {
        error_log('Simple Shipping Replacement: Invalid pickup location format');
    }
}

// Test function to verify it's working
add_action('init', function() {
    if (isset($_GET['test_pickup_replacement']) && current_user_can('manage_options')) {
        echo "<h2>Simple Shipping Replacement Test</h2>";
        
        // Check if function exists
        if (function_exists('simple_replace_shipping_with_pickup')) {
            echo "<p>✅ Function exists</p>";
        } else {
            echo "<p>❌ Function not found</p>";
        }
        
        // Check if hook is registered
        if (has_action('woocommerce_checkout_update_order_meta', 'simple_replace_shipping_with_pickup')) {
            echo "<p>✅ Hook is registered</p>";
        } else {
            echo "<p>❌ Hook not registered</p>";
        }
        
        echo "<p>Add this code to your theme's functions.php or create a simple plugin with it.</p>";
        exit;
    }
});
?>
