/* Minimal styling for pickup location fields to match WooCommerce defaults */

/* Loading indicator for dropdowns */
#pickup_location_fields select.loading {
    background-image: url('data:image/svg+xml;charset=utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><circle cx="10" cy="10" r="8" fill="none" stroke="%23999" stroke-width="2"/><circle cx="10" cy="10" r="8" fill="none" stroke="%23666" stroke-width="2" stroke-dasharray="12.566" stroke-dashoffset="12.566" transform-origin="10 10"><animateTransform attributeName="transform" type="rotate" values="0 10 10;360 10 10" dur="1s" repeatCount="indefinite"/></circle></svg>');
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px 16px;
}

/* Customer order details styling */
.pickup-location-table {
    margin-top: 15px;
}

.pickup-location-table th {
    font-weight: bold;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.pickup-location-table td {
    padding: 8px 12px;
    border-bottom: 1px solid #dee2e6;
}

/* Smooth transitions for showing/hiding fields */
#pickup_region_field,
#pickup_district_field,
#pickup_location_field {
    transition: opacity 0.2s ease-in-out;
}

/* Show pickup fields only when specific shipping methods are selected */
/* This provides a CSS fallback in case JavaScript doesn't work properly */

/* Hide pickup fields by default */
#pickup_location_fields {
    display: none;
}

/* Show pickup fields when specific shipping methods are selected */
.woocommerce-checkout input[value*="順豐到付 自取點"]:checked ~ * #pickup_location_fields,
.woocommerce-checkout input[value*="免運費 (順豐自取點)"]:checked ~ * #pickup_location_fields {
    display: block !important;
}

/* Alternative selectors for different checkout layouts */
body:has(input[value*="順豐到付 自取點"]:checked) #pickup_location_fields,
body:has(input[value*="免運費 (順豐自取點)"]:checked) #pickup_location_fields {
    display: block !important;
}

/* Style for shipping address fields when they're replaced with pickup location */
.pickup-address-override {
    background-color: #f0f8ff !important;
    border: 2px solid #4CAF50 !important;
    color: #2e7d32 !important;
}

.pickup-address-override:focus {
    background-color: #f0f8ff !important;
    border-color: #4CAF50 !important;
}

/* Add a visual indicator when shipping address is replaced */
.pickup-address-replaced::after {
    content: " 📍";
    color: #4CAF50;
}

/* Responsive design - only essential mobile adjustments */
@media (max-width: 768px) {
    #pickup_location_fields select {
        font-size: 16px; /* Prevents zoom on iOS */
    }
}