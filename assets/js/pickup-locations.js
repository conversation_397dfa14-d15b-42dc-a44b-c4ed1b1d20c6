jQuery(document).ready(function($) {
    // Location data from PHP
    var locations = pickupLocations;

    // Debug: Log the locations data to console
    console.log('Pickup Locations Data:', locations);
    console.log('Available pickup types:', Object.keys(locations || {}));
    console.log('Show for shipping methods:', showForShippingMethods);

    // Initialize after a delay to ensure Bricks Builder has loaded
    function initializePickupFields() {
        console.log('Initializing pickup location fields...');

        // Check if pickup location fields exist
        if ($('#pickup_location_fields').length === 0) {
            console.log('Pickup location fields not found, retrying...');
            setTimeout(initializePickupFields, 1000);
            return;
        }

        console.log('Pickup location fields found, setting up functionality');
        setupPickupLocationLogic();
    }

    // Setup the main pickup location logic
    function setupPickupLocationLogic() {

    // Initially hide all pickup fields (container is hidden by default)
    $('#pickup_region_field, #pickup_district_field, #pickup_location_field').hide();

    // Get shipping methods that should show pickup fields from PHP configuration
    var showForShippingMethods = pickupLocationConfig && pickupLocationConfig.showForShippingMethods ?
        pickupLocationConfig.showForShippingMethods : [
            '順豐到付 自取點',
            '免運費 (順豐自取點)',
            '自取點',
            'pickup',
            'self_pickup'
        ];

    // Function to check if pickup fields should be shown
    function shouldShowPickupFields() {
        // Try multiple selectors to work with different checkout layouts (including Bricks Builder)
        var selectedMethod = '';
        var selectedMethodLabel = '';

        // Try standard WooCommerce selectors
        var $checkedInput = $('input[name^="shipping_method"]:checked');
        if ($checkedInput.length > 0) {
            selectedMethod = $checkedInput.val();
            selectedMethodLabel = $checkedInput.closest('li').find('label').text();
        }

        // Try alternative selectors for Bricks Builder or custom layouts
        if (!selectedMethod) {
            $checkedInput = $('input[type="radio"][name*="shipping"]:checked');
            if ($checkedInput.length > 0) {
                selectedMethod = $checkedInput.val();
                selectedMethodLabel = $checkedInput.next('label').text() || $checkedInput.parent().text();
            }
        }

        // Try select dropdown (some themes use select instead of radio)
        if (!selectedMethod) {
            var $select = $('select[name^="shipping_method"]');
            if ($select.length > 0) {
                selectedMethod = $select.val();
                selectedMethodLabel = $select.find('option:selected').text();
            }
        }

        if (!selectedMethod) {
            console.log('No shipping method found');
            return false; // Hide fields if no method selected
        }

        console.log('Selected shipping method:', selectedMethod);
        console.log('Selected shipping method label:', selectedMethodLabel);

        // Check if the selected method or its label contains any keywords that should show pickup fields
        for (var i = 0; i < showForShippingMethods.length; i++) {
            if (selectedMethod.indexOf(showForShippingMethods[i]) !== -1 ||
                selectedMethodLabel.indexOf(showForShippingMethods[i]) !== -1) {
                console.log('Showing pickup fields for shipping method:', showForShippingMethods[i]);
                return true;
            }
        }
        return false; // Hide fields for all other shipping methods
    }

    // Function to show/hide pickup location fields based on shipping method
    function togglePickupLocationFields() {
        if (shouldShowPickupFields()) {
            console.log('Showing pickup location fields');
            $('#pickup_location_fields').show();
        } else {
            console.log('Hiding pickup location fields');
            $('#pickup_location_fields').hide();
            // Reset all pickup selections when hiding
            $('#pickup_type, #pickup_region, #pickup_district, #pickup_location').val('');
            $('#pickup_region_field, #pickup_district_field, #pickup_location_field').hide();
        }
    }

    // Check shipping method on page load
    setTimeout(function() {
        togglePickupLocationFields();
    }, 500); // Small delay to ensure Bricks Builder has loaded

    // Listen for shipping method changes - multiple selectors for compatibility
    $(document).on('change', 'input[name^="shipping_method"]', function() {
        console.log('Shipping method changed (standard)');
        setTimeout(togglePickupLocationFields, 100);
    });

    $(document).on('change', 'input[type="radio"][name*="shipping"]', function() {
        console.log('Shipping method changed (alternative)');
        setTimeout(togglePickupLocationFields, 100);
    });

    $(document).on('change', 'select[name^="shipping_method"]', function() {
        console.log('Shipping method changed (select)');
        setTimeout(togglePickupLocationFields, 100);
    });

    // Listen for checkout updates (WooCommerce AJAX)
    $(document).on('updated_checkout', function() {
        console.log('Checkout updated, checking shipping method');
        setTimeout(togglePickupLocationFields, 200);
    });

    // Listen for Bricks Builder specific events (if any)
    $(document).on('bricks/ajax/completed', function() {
        console.log('Bricks AJAX completed, checking shipping method');
        setTimeout(togglePickupLocationFields, 200);
    });

    // Fallback: Check periodically for changes (for complex builders)
    var lastShippingMethod = '';
    setInterval(function() {
        var currentMethod = $('input[name^="shipping_method"]:checked').val() ||
                           $('input[type="radio"][name*="shipping"]:checked').val() ||
                           $('select[name^="shipping_method"]').val();

        if (currentMethod && currentMethod !== lastShippingMethod) {
            console.log('Shipping method changed (periodic check):', currentMethod);
            lastShippingMethod = currentMethod;
            togglePickupLocationFields();
        }
    }, 1000); // Check every second

    // Handle pickup type selection - Updated for different data structures
    $('#pickup_type').change(function() {
        var selectedType = $(this).val();
        var regionSelect = $('#pickup_region');
        var districtSelect = $('#pickup_district');
        var locationSelect = $('#pickup_location');

        console.log('Selected Pickup Type:', selectedType);

        // Clear and hide all subsequent dropdowns
        regionSelect.empty().append('<option value="">Choose a region...</option>');
        districtSelect.empty().append('<option value="">Choose a district...</option>');
        locationSelect.empty().append('<option value="">Choose a location...</option>');
        $('#pickup_region_field, #pickup_district_field, #pickup_location_field').hide();

        if (selectedType && locations[selectedType]) {
            console.log('Pickup type data found:', Object.keys(locations[selectedType]));

            // Populate region dropdown with available regions for this pickup type
            $.each(locations[selectedType], function(region, districts) {
                console.log('Adding region:', region);
                regionSelect.append('<option value="' + region + '">' + region + '</option>');
            });

            // Update labels based on pickup type
            if (selectedType === 'selfpickup') {
                $('#pickup_district_field label').text('Select District');
                $('#pickup_location_field label').text('Select Pickup Location');
            } else if (selectedType === 'locker') {
                $('#pickup_district_field label').text('Select District');
                $('#pickup_location_field label').text('Select Locker');
            }

            // Show region dropdown
            $('#pickup_region_field').show();
        } else {
            console.log('No data found for pickup type:', selectedType);
        }

        // Trigger checkout update
        $('body').trigger('update_checkout');
    });

    // Handle region selection - Only for self-pickup
    $('#pickup_region').change(function() {
        var selectedType = $('#pickup_type').val();
        var selectedRegion = $(this).val();
        var districtSelect = $('#pickup_district');
        var locationSelect = $('#pickup_location');

        console.log('Selected Region:', selectedRegion, 'for Type:', selectedType);

        // Clear and hide subsequent dropdowns
        districtSelect.empty().append('<option value="">Choose a district...</option>');
        locationSelect.empty().append('<option value="">Choose a location...</option>');
        $('#pickup_location_field').hide();

        // Process for both pickup types since they now have the same structure
        if (selectedType && selectedRegion && locations[selectedType] && locations[selectedType][selectedRegion]) {
            console.log('Region data found:', locations[selectedType][selectedRegion]);

            // Populate district dropdown
            $.each(locations[selectedType][selectedRegion], function(district, locationList) {
                console.log('Adding district:', district);
                // Use the district name as both value and display text for Chinese characters
                districtSelect.append('<option value="' + district + '">' + district + '</option>');
            });
            $('#pickup_district_field').show();
        } else {
            console.log('No data found for type:', selectedType, 'region:', selectedRegion);
            $('#pickup_district_field').hide();
        }

        // Trigger checkout update
        $('body').trigger('update_checkout');
    });

    // Handle district selection - Updated for different data structures
    $('#pickup_district').change(function() {
        var selectedType = $('#pickup_type').val();
        var selectedRegion = $('#pickup_region').val();
        var selectedDistrict = $(this).val();
        var locationSelect = $('#pickup_location');

        console.log('Selected District:', selectedDistrict, 'for Type:', selectedType, 'Region:', selectedRegion);

        // Clear location dropdown with appropriate placeholder
        var placeholder = selectedType === 'locker' ? 'Choose a locker...' : 'Choose a location...';
        locationSelect.empty().append('<option value="">' + placeholder + '</option>');

        var locationData = null;

        // Both pickup types now use the same structure: type -> region -> district -> locations
        if (selectedType && selectedRegion && selectedDistrict &&
            locations[selectedType] &&
            locations[selectedType][selectedRegion] &&
            locations[selectedType][selectedRegion][selectedDistrict]) {
            locationData = locations[selectedType][selectedRegion][selectedDistrict];
        }

        if (locationData) {
            console.log('District data found:', locationData);

            // Populate location dropdown
            $.each(locationData, function(index, location) {
                console.log('Adding location:', location);

                // Extract the address (third part after splitting by tab)
                var parts = location.split('\t');
                var displayName = '';

                if (parts.length >= 3) {
                    // Use the address (third part), clean up any trailing codes like ^ID^
                    displayName = parts[2].replace(/\s*\^[^^\s]*\^.*$/, '').trim();
                } else if (parts.length >= 2) {
                    // Fallback to name (second part) if address not available
                    displayName = parts[1];
                } else {
                    // Fallback to the whole string if no tabs found
                    displayName = location;
                }

                console.log('Display name will be:', displayName);
                locationSelect.append('<option value="' + location + '">' + displayName + '</option>');
            });
            $('#pickup_location_field').show();
        } else {
            console.log('No data found for type:', selectedType, 'region:', selectedRegion, 'district:', selectedDistrict);
            $('#pickup_location_field').hide();
        }

        // Trigger checkout update
        $('body').trigger('update_checkout');
    });

    // Handle location selection - UNCHANGED
    $('#pickup_location').change(function() {
        console.log('Selected Location:', $(this).val());
        // Trigger checkout update
        $('body').trigger('update_checkout');
    });

    // Add loading states
    function showLoading(element) {
        element.prop('disabled', true).addClass('loading');
    }

    function hideLoading(element) {
        element.prop('disabled', false).removeClass('loading');
    }

    // Handle AJAX loading states
    $(document).on('checkout_error', function() {
        hideLoading($('#pickup_type, #pickup_region, #pickup_district, #pickup_location'));
    });

    } // End of setupPickupLocationLogic function

    // Start initialization
    initializePickupFields();
});