// Simple debug script - paste this in your browser console on the checkout page
// This will show you exactly what shipping method values are being used

console.log('=== SIMPLE SHIPPING DEBUG ===');

// Function to log all shipping methods
function logShippingMethods() {
    console.log('--- Current Shipping Methods ---');
    
    // Check all possible shipping method inputs
    var inputs = document.querySelectorAll('input[name*="shipping"], select[name*="shipping"]');
    
    inputs.forEach(function(input, index) {
        if (input.type === 'radio' || input.type === 'checkbox') {
            console.log('Input ' + index + ':', {
                name: input.name,
                value: input.value,
                checked: input.checked,
                label: input.nextElementSibling ? input.nextElementSibling.textContent.trim() : 'No label found'
            });
        } else if (input.tagName === 'SELECT') {
            console.log('Select ' + index + ':', {
                name: input.name,
                value: input.value,
                selectedText: input.options[input.selectedIndex] ? input.options[input.selectedIndex].text : 'None'
            });
        }
    });
    
    // Also check for any checked shipping methods
    var checkedInputs = document.querySelectorAll('input[name*="shipping"]:checked');
    console.log('--- Currently Selected ---');
    checkedInputs.forEach(function(input, index) {
        console.log('Selected ' + index + ':', {
            name: input.name,
            value: input.value,
            label: input.nextElementSibling ? input.nextElementSibling.textContent.trim() : 'No label found'
        });
    });
}

// Log current state
logShippingMethods();

// Monitor changes
document.addEventListener('change', function(e) {
    if (e.target.name && e.target.name.includes('shipping')) {
        console.log('--- Shipping Method Changed ---');
        console.log('Changed element:', {
            name: e.target.name,
            value: e.target.value,
            checked: e.target.checked,
            label: e.target.nextElementSibling ? e.target.nextElementSibling.textContent.trim() : 'No label found'
        });
        
        // Log all current selections after change
        setTimeout(logShippingMethods, 100);
    }
});

console.log('=== DEBUG SETUP COMPLETE ===');
console.log('Now select different shipping methods and watch the console output');
