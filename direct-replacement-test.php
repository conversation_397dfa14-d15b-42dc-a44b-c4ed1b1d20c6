<?php
/**
 * Direct replacement test - bypasses all hooks and directly modifies orders
 * This will help us verify if the replacement logic itself works
 */

// Only run for admins
if (!current_user_can('manage_options')) {
    die('Access denied');
}

// Test the replacement on existing orders
if (isset($_GET['test_order_id'])) {
    $order_id = intval($_GET['test_order_id']);
    $order = wc_get_order($order_id);
    
    if (!$order) {
        die('Order not found');
    }
    
    echo "<h2>Testing Direct Replacement on Order #$order_id</h2>";
    
    echo "<h3>Before Replacement:</h3>";
    echo "<p><strong>Shipping Address:</strong><br>" . $order->get_formatted_shipping_address() . "</p>";
    
    // Test pickup data
    $test_pickup_data = "852M\t上環文樂商廈順豐站\t香港香港島中西區中上環文咸東街91號及永樂街117號文樂商業大廈地下A號舖 ^852M^";
    
    // Parse and apply
    $parts = explode("\t", $test_pickup_data);
    if (count($parts) >= 3) {
        $pickup_name = $parts[1];
        $pickup_address = preg_replace('/\s*\^[^^\s]*\^.*$/', '', $parts[2]);
        
        // Apply replacement
        $order->set_shipping_first_name($pickup_name);
        $order->set_shipping_last_name('');
        $order->set_shipping_company($pickup_name);
        $order->set_shipping_address_1($pickup_address);
        $order->set_shipping_address_2('');
        $order->set_shipping_city('香港島');
        $order->set_shipping_state('');
        $order->set_shipping_postcode('');
        $order->set_shipping_country('HK');
        
        // Save
        $order->save();
        
        echo "<h3>After Replacement:</h3>";
        echo "<p><strong>Shipping Address:</strong><br>" . $order->get_formatted_shipping_address() . "</p>";
        echo "<p>✅ Direct replacement successful!</p>";
        
        // Add note
        $order->add_order_note('Shipping address manually replaced with pickup location for testing');
    }
    
    exit;
}

echo "<h2>Direct Replacement Test</h2>";

// Show recent orders
$orders = wc_get_orders(array(
    'limit' => 10,
    'status' => array('processing', 'completed', 'pending', 'on-hold')
));

if (!empty($orders)) {
    echo "<h3>Recent Orders - Click to Test Replacement:</h3>";
    foreach ($orders as $order) {
        $pickup_location = $order->get_meta('_pickup_location');
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
        echo "<strong>Order #" . $order->get_id() . "</strong> - " . $order->get_status() . "<br>";
        echo "<strong>Current Shipping:</strong> " . $order->get_shipping_first_name() . " " . $order->get_shipping_last_name() . "<br>";
        echo "<strong>Address:</strong> " . $order->get_shipping_address_1() . "<br>";
        if ($pickup_location) {
            echo "<strong>Has Pickup Location:</strong> ✅<br>";
        } else {
            echo "<strong>Has Pickup Location:</strong> ❌<br>";
        }
        echo "<a href='?test_order_id=" . $order->get_id() . "' style='background: #0073aa; color: white; padding: 5px 10px; text-decoration: none;'>Test Replacement</a>";
        echo "</div>";
    }
} else {
    echo "<p>No orders found</p>";
}

// Test the filter function directly
echo "<hr><h3>Test Filter Function Directly</h3>";

// Simulate checkout data
$test_data = array(
    'billing_first_name' => 'Test',
    'billing_last_name' => 'Customer',
    'billing_email' => '<EMAIL>',
    'shipping_first_name' => 'Original',
    'shipping_last_name' => 'Name',
    'shipping_address_1' => 'Original Address',
    'shipping_city' => 'Original City',
    'shipping_country' => 'HK',
    'pickup_location' => '852M\t上環文樂商廈順豐站\t香港香港島中西區中上環文咸東街91號及永樂街117號文樂商業大廈地下A號舖 ^852M^',
    'shipping_method' => array('順豐到付 自取點')
);

echo "<h4>Original Data:</h4>";
echo "<pre>" . print_r($test_data, true) . "</pre>";

// Test our filter function
if (class_exists('WC_Pickup_Locations')) {
    $plugin = new WC_Pickup_Locations();
    $modified_data = $plugin->modify_checkout_posted_data($test_data);
    
    echo "<h4>Modified Data:</h4>";
    echo "<pre>" . print_r($modified_data, true) . "</pre>";
    
    if ($modified_data['shipping_first_name'] !== $test_data['shipping_first_name']) {
        echo "<p>✅ Filter function is working correctly!</p>";
    } else {
        echo "<p>❌ Filter function did not modify the data</p>";
    }
} else {
    echo "<p>❌ Plugin class not found</p>";
}

echo "<hr>";
echo "<p><strong>Instructions:</strong></p>";
echo "<ol>";
echo "<li>Test direct replacement on existing orders above</li>";
echo "<li>Check if the filter function works correctly</li>";
echo "<li>If both work, the issue is with hook timing in Bricks Builder</li>";
echo "</ol>";
?>
